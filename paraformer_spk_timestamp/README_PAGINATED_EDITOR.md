# 分页文本编辑器设计方案

## 概述

针对当前Gradio表格组件的限制（无法设置列权限、缺乏分页功能），我们设计了一个全新的分页式文本编辑器，提供更好的用户体验和更强的功能。

## 设计特点

### 1. 卡片式布局
- **独立编辑区域**：每个文本段落显示为独立的卡片，便于逐个编辑
- **清晰的视觉分离**：卡片间有明确的边界，避免编辑混乱
- **响应式设计**：支持不同屏幕尺寸的自适应显示

### 2. 音频集成
- **片段音频播放**：每个文本卡片配有对应的音频播放器
- **时间戳显示**：清晰显示每个片段的开始和结束时间
- **音频导出**：支持导出修改后的音频片段

### 3. 分页功能
- **可配置页面大小**：支持5-50个片段/页的灵活配置
- **页面导航**：前一页/后一页按钮，页面信息显示
- **性能优化**：只渲染当前页面内容，提高大数据集处理性能

### 4. 批量操作
- **批量保存**：一键保存当前页面或全部修改
- **批量导出**：支持TXT、SRT等格式的批量导出
- **批量重置**：快速恢复到原始状态

## 技术实现

### 文件结构
```
paraformer_spk_timestamp/
├── components/
│   └── paginated_editor.py          # 分页编辑器核心组件
├── static/
│   └── paginated_editor.css         # 样式文件
├── webui_asr_paginated.py           # 集成分页编辑器的WebUI
├── demo_paginated_editor.py         # 演示脚本
└── README_PAGINATED_EDITOR.md       # 本文档
```

### 核心组件

#### 1. PaginatedTextEditor 类
```python
class PaginatedTextEditor:
    def __init__(self, page_size: int = 10):
        self.page_size = page_size
        self.current_page = 1
        self.segments_data = []
        self.audio_file_path = None
```

**主要功能：**
- 数据分页管理
- 音频片段提取
- 页面导航控制
- 修改状态跟踪

#### 2. 音频处理
使用FFmpeg进行音频片段提取：
```python
def _extract_audio_segments(self):
    """提取音频片段"""
    for segment in self.segments_data:
        cmd = [
            'ffmpeg', '-i', self.audio_file_path,
            '-ss', str(start_time),
            '-to', str(end_time),
            '-c:a', 'pcm_s16le',
            output_file
        ]
        subprocess.run(cmd, check=True)
```

#### 3. 界面组件
- **文本编辑区域**：支持多行文本编辑，实时保存
- **说话人编辑**：独立的说话人标签编辑框
- **音频播放器**：HTML5音频播放器，支持下载
- **操作按钮**：保存、重置、导出等功能按钮

## 用户界面设计

### 布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                        页面控制栏                            │
│  ◀ 前一页    第 X 页 / 共 Y 页    后一页 ▶                   │
├─────────────────────────────────────────────────────────────┤
│                        批量操作栏                            │
│  💾 保存全部  🎵 导出音频  📄 导出文本  每页显示: [10] ▼     │
├─────────────────────────────────────────────────────────────┤
│                        文本编辑区域                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │  📝 Text 1                                    🟢 原始   │ │
│ │  ┌─────────────────────┐  ┌─────────────────────────┐   │ │
│ │  │ 识别文本编辑框       │  │     🎵 音频播放器        │   │ │
│ │  │                    │  │                        │   │ │
│ │  │                    │  │   00:00:01 → 00:00:03   │   │ │
│ │  └─────────────────────┘  │                        │   │ │
│ │  ┌─────────────────────┐  │   💾 保存    🔄 重置    │   │ │
│ │  │ 说话人: spk1        │  └─────────────────────────┘   │ │
│ │  └─────────────────────┘                              │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │  📝 Text 2                                    🔴 已修改 │ │
│ │  ...                                                   │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 交互特性
1. **实时编辑**：文本修改即时生效，无需额外保存步骤
2. **状态指示**：清晰显示哪些片段已被修改（🟢原始 / 🔴已修改）
3. **音频同步**：编辑文本时可同时播放对应音频片段
4. **快捷操作**：支持键盘快捷键（Ctrl+S保存等）

## 优势对比

### 相比原有表格方案的优势：

| 特性 | 原有表格 | 新分页编辑器 |
|------|----------|-------------|
| 列权限控制 | ❌ 无法设置 | ✅ 完全可控 |
| 分页功能 | ❌ 无分页 | ✅ 灵活分页 |
| 音频集成 | ❌ 无音频 | ✅ 片段播放 |
| 编辑体验 | ⚠️ 表格编辑 | ✅ 专业编辑器 |
| 性能表现 | ⚠️ 大数据卡顿 | ✅ 分页优化 |
| 视觉效果 | ⚠️ 单调表格 | ✅ 美观卡片 |
| 批量操作 | ⚠️ 有限支持 | ✅ 丰富功能 |

## 使用方法

### 1. 运行演示
```bash
cd paraformer_spk_timestamp
python demo_paginated_editor.py
```

### 2. 集成到现有系统
```bash
python webui_asr_paginated.py
```

### 3. 自定义配置
```python
# 创建编辑器实例
editor = PaginatedTextEditor(page_size=15)

# 初始化数据
editor.initialize_data(segments, audio_file)

# 创建界面
interface = editor.create_interface()
```

## 配置选项

### 分页设置
- **page_size**: 每页显示的片段数量（5-50）
- **auto_save**: 是否启用自动保存
- **audio_enabled**: 是否启用音频功能

### 音频设置
- **audio_format**: 音频输出格式（wav/mp3）
- **audio_quality**: 音频质量设置
- **extract_audio**: 是否自动提取音频片段

### 界面设置
- **theme**: 界面主题（light/dark）
- **card_height**: 卡片高度设置
- **responsive**: 是否启用响应式布局

## 扩展功能

### 计划中的功能
1. **协作编辑**：多用户同时编辑支持
2. **版本控制**：编辑历史记录和回滚
3. **智能建议**：基于AI的文本修正建议
4. **快捷键支持**：完整的键盘快捷键方案
5. **导出格式**：更多导出格式支持（Word、PDF等）

### 插件系统
- **语法检查插件**：实时语法和拼写检查
- **翻译插件**：多语言翻译支持
- **统计插件**：文本统计和分析
- **备份插件**：自动备份和恢复

## 技术要求

### 依赖项
```
gradio >= 4.0.0
ffmpeg >= 4.0
pandas >= 1.3.0
```

### 系统要求
- Python 3.8+
- 内存: 最少2GB，推荐4GB+
- 存储: 临时音频文件需要额外空间
- 网络: 用于模型下载和更新

## 总结

新的分页文本编辑器设计解决了原有表格方案的所有主要问题：

1. **功能完整性**：提供了列权限控制、分页、音频集成等完整功能
2. **用户体验**：卡片式布局更直观，编辑更便捷
3. **性能优化**：分页加载大幅提升大数据集处理性能
4. **扩展性强**：模块化设计便于后续功能扩展

这个方案不仅解决了当前的技术限制，还为未来的功能扩展奠定了良好基础。建议优先实现核心功能，然后逐步添加高级特性。
