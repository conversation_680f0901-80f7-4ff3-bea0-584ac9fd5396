#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页式文本编辑器组件
"""

import os
import tempfile
import subprocess
import gradio as gr
from typing import List, Dict, Tuple, Optional
import json

class PaginatedTextEditor:
    def __init__(self, page_size: int = 10):
        self.page_size = page_size
        self.current_page = 1
        self.total_pages = 1
        self.segments_data = []
        self.audio_file_path = None
        self.temp_audio_dir = None
        
    def initialize_data(self, segments: List[Dict], audio_file: str = None):
        """初始化数据"""
        self.segments_data = segments
        self.audio_file_path = audio_file
        self.total_pages = max(1, (len(segments) + self.page_size - 1) // self.page_size)
        self.current_page = 1
        
        # 创建临时音频目录
        if audio_file:
            self.temp_audio_dir = tempfile.mkdtemp(prefix="asr_audio_")
            self._extract_audio_segments()
    
    def _extract_audio_segments(self):
        """提取音频片段"""
        if not self.audio_file_path or not self.temp_audio_dir:
            return
        
        for i, segment in enumerate(self.segments_data):
            try:
                start_time = self._convert_timestamp_to_seconds(segment.get('start_time', ''))
                end_time = self._convert_timestamp_to_seconds(segment.get('end_time', ''))
                
                output_file = os.path.join(self.temp_audio_dir, f"segment_{i+1:03d}.wav")
                
                cmd = [
                    'ffmpeg', '-i', self.audio_file_path,
                    '-ss', str(start_time),
                    '-to', str(end_time),
                    '-c:a', 'pcm_s16le',  # 使用WAV格式确保兼容性
                    '-y',  # 覆盖输出文件
                    output_file
                ]
                
                subprocess.run(cmd, check=True, capture_output=True)
                segment['audio_file'] = output_file
                
            except Exception as e:
                print(f"提取音频片段 {i+1} 失败: {e}")
                segment['audio_file'] = None
    
    def _convert_timestamp_to_seconds(self, timestamp: str) -> float:
        """将时间戳转换为秒数"""
        try:
            # 支持 HH:MM:SS,mmm 和 HH:MM:SS.mmm 格式
            if ',' in timestamp:
                time_part, ms_part = timestamp.split(',')
            elif '.' in timestamp:
                time_part, ms_part = timestamp.split('.')
            else:
                time_part, ms_part = timestamp, "000"
            
            h, m, s = map(int, time_part.split(':'))
            ms = int(ms_part)
            
            return h * 3600 + m * 60 + s + ms / 1000.0
        except:
            return 0.0
    
    def get_current_page_data(self) -> List[Dict]:
        """获取当前页面数据"""
        start_idx = (self.current_page - 1) * self.page_size
        end_idx = min(start_idx + self.page_size, len(self.segments_data))
        return self.segments_data[start_idx:end_idx]
    
    def create_page_components(self):
        """创建页面组件"""
        components = []
        
        # 页面控制栏
        with gr.Row():
            prev_btn = gr.Button("◀ 前一页", size="sm", interactive=self.current_page > 1)
            page_info = gr.HTML(f"<center>第 {self.current_page} 页 / 共 {self.total_pages} 页</center>")
            next_btn = gr.Button("后一页 ▶", size="sm", interactive=self.current_page < self.total_pages)
        
        # 批量操作栏
        with gr.Row():
            save_all_btn = gr.Button("💾 保存全部", variant="primary", size="sm")
            export_audio_btn = gr.Button("🎵 导出音频", variant="secondary", size="sm")
            batch_size_slider = gr.Slider(
                minimum=5, maximum=50, value=self.page_size, step=5,
                label="每页显示数量", interactive=True
            )
        
        # 文本编辑卡片容器
        cards_container = gr.Column()
        
        components.extend([
            prev_btn, page_info, next_btn,
            save_all_btn, export_audio_btn, batch_size_slider,
            cards_container
        ])
        
        return components
    
    def create_text_cards(self, page_data: List[Dict]):
        """创建文本编辑卡片"""
        cards = []
        
        for i, segment in enumerate(page_data):
            segment_id = segment.get('id', i + 1)
            start_time = segment.get('start_time', '')
            end_time = segment.get('end_time', '')
            speaker = segment.get('speaker', '')
            text = segment.get('text', '')
            audio_file = segment.get('audio_file', None)
            
            with gr.Group():
                with gr.Row():
                    # 左侧：文本编辑区域
                    with gr.Column(scale=3):
                        gr.HTML(f"<h4>Text {segment_id}</h4>")
                        
                        text_input = gr.Textbox(
                            value=text,
                            label="识别文本",
                            lines=3,
                            max_lines=5,
                            interactive=True,
                            elem_id=f"text_{segment_id}"
                        )
                        
                        if speaker:  # 如果有说话人信息
                            speaker_input = gr.Textbox(
                                value=speaker,
                                label="说话人",
                                lines=1,
                                interactive=True,
                                elem_id=f"speaker_{segment_id}"
                            )
                        else:
                            speaker_input = None
                    
                    # 右侧：音频播放和控制
                    with gr.Column(scale=2):
                        if audio_file and os.path.exists(audio_file):
                            audio_player = gr.Audio(
                                value=audio_file,
                                label="音频片段",
                                interactive=False,
                                show_download_button=True
                            )
                        else:
                            audio_player = gr.HTML(
                                "<div style='text-align: center; color: #666;'>音频不可用</div>"
                            )
                        
                        # 时间信息和操作按钮
                        time_info = gr.HTML(
                            f"<small>{start_time} → {end_time}</small>"
                        )
                        
                        with gr.Row():
                            save_btn = gr.Button("💾", size="sm", elem_id=f"save_{segment_id}")
                            reset_btn = gr.Button("🔄", size="sm", elem_id=f"reset_{segment_id}")
            
            cards.append({
                'segment_id': segment_id,
                'text_input': text_input,
                'speaker_input': speaker_input,
                'audio_player': audio_player,
                'save_btn': save_btn,
                'reset_btn': reset_btn,
                'original_data': segment.copy()
            })
        
        return cards
    
    def navigate_page(self, direction: str):
        """页面导航"""
        if direction == "prev" and self.current_page > 1:
            self.current_page -= 1
        elif direction == "next" and self.current_page < self.total_pages:
            self.current_page += 1
        
        return self.get_current_page_data()
    
    def update_page_size(self, new_size: int):
        """更新每页显示数量"""
        self.page_size = new_size
        self.total_pages = max(1, (len(self.segments_data) + self.page_size - 1) // self.page_size)
        
        # 调整当前页面以确保不超出范围
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages
        
        return self.get_current_page_data()
    
    def save_segment_changes(self, segment_id: int, new_text: str, new_speaker: str = None):
        """保存单个片段的修改"""
        for segment in self.segments_data:
            if segment.get('id') == segment_id:
                segment['text'] = new_text
                if new_speaker is not None:
                    segment['speaker'] = new_speaker
                return True
        return False
    
    def reset_segment(self, segment_id: int):
        """重置单个片段到原始状态"""
        # 这里需要从缓存管理器获取原始数据
        pass
    
    def export_modified_audio(self):
        """导出修改后的音频（合并所有片段）"""
        if not self.temp_audio_dir:
            return None
        
        try:
            # 创建音频文件列表
            audio_files = []
            for segment in self.segments_data:
                audio_file = segment.get('audio_file')
                if audio_file and os.path.exists(audio_file):
                    audio_files.append(audio_file)
            
            if not audio_files:
                return None
            
            # 使用ffmpeg合并音频
            output_file = os.path.join(tempfile.gettempdir(), "merged_audio.wav")
            
            # 创建文件列表
            list_file = os.path.join(self.temp_audio_dir, "file_list.txt")
            with open(list_file, 'w') as f:
                for audio_file in audio_files:
                    f.write(f"file '{audio_file}'\n")
            
            cmd = [
                'ffmpeg', '-f', 'concat', '-safe', '0',
                '-i', list_file,
                '-c', 'copy',
                '-y',
                output_file
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            return output_file
            
        except Exception as e:
            print(f"导出音频失败: {e}")
            return None
    
    def cleanup(self):
        """清理临时文件"""
        if self.temp_audio_dir and os.path.exists(self.temp_audio_dir):
            import shutil
            try:
                shutil.rmtree(self.temp_audio_dir)
            except Exception as e:
                print(f"清理临时文件失败: {e}")
