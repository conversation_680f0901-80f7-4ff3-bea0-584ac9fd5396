#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带分页编辑器的ASR WebUI
"""

import os
import sys
import configparser
import platform
import time
import threading
import tempfile
import funasr
import gradio as gr
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import json

# 添加组件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'components'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from paginated_editor import PaginatedTextEditor
from web_cache_manager import WebCacheManager

class AsrWebUIPaginated:
    def __init__(self):
        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.punc_models_config = {}
        self.spk_models_config = {}
        self.model_cache = {}
        
        # 当前识别结果
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        self.current_audio_file = None
        
        # Web缓存管理器和分页编辑器
        self.cache_manager = WebCacheManager()
        self.paginated_editor = PaginatedTextEditor(page_size=10)
        
        # 加载模型配置
        self.load_model_config()
    
    def load_model_config(self):
        """加载模型配置文件"""
        config_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf"),
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    print(f"读取配置文件 {path} 失败: {e}")
                    return

        if not loaded_path:
            print("错误: 未找到 model.conf 文件。请确保它位于项目目录下。")
            return

        print(f"成功从 {loaded_path} 加载模型配置。")

        def populate_config(section_name, model_dict):
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    model_dict[name] = path_or_id
            else:
                print(f"警告: 配置文件中未找到 [{section_name}] 部分。")

        # 加载各类模型配置
        populate_config("asr_models_dir", self.asr_models_config)
        populate_config("asr_seaco_models_dir", self.asr_models_config)
        populate_config("vad_models_dir", self.vad_models_config)
        populate_config("punc_models_dir", self.punc_models_config)
        populate_config("spk_models_dir", self.spk_models_config)

    def get_model_choices(self, model_type: str):
        """根据模型类型获取对应的模型选项"""
        if model_type == "paraformer":
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')
                asr_models = {}
                if "asr_models_dir" in config:
                    for name, path_or_id in config["asr_models_dir"].items():
                        asr_models[name] = path_or_id
                return list(asr_models.keys())
        elif model_type == "seaco":
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')
                asr_models = {}
                if "asr_seaco_models_dir" in config:
                    for name, path_or_id in config["asr_seaco_models_dir"].items():
                        asr_models[name] = path_or_id
                return list(asr_models.keys())
        
        return list(self.asr_models_config.keys())

    def get_vad_model_choices(self):
        """获取VAD模型选项"""
        choices = ["None (不使用)"] + list(self.vad_models_config.keys())
        return choices

    def get_punc_model_choices(self):
        """获取标点模型选项"""
        choices = ["None (不使用)"] + list(self.punc_models_config.keys())
        return choices

    def get_spk_model_choices(self):
        """获取说话人模型选项"""
        choices = ["None (不使用)"] + list(self.spk_models_config.keys())
        return choices

    def update_model_choices(self, model_type: str):
        """根据模型类型更新ASR模型选项"""
        choices = self.get_model_choices(model_type)
        hotword_visible = (model_type == "seaco")
        
        return (
            gr.Dropdown(choices=choices, value=choices[0] if choices else None),
            gr.Row(visible=hotword_visible),
        )

    def format_timestamp(self, milliseconds: int, srt_format: bool = False) -> str:
        """将毫秒转换为时间戳格式"""
        seconds = milliseconds // 1000
        ms = milliseconds % 1000
        minutes = seconds // 60
        seconds %= 60
        hours = minutes // 60
        minutes %= 60
        if srt_format:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{int(ms):03d}"
        else:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}.{int(ms):03d}"

    def start_recognition(
        self, 
        audio_file,
        model_type: str,
        device: str,
        asr_model: str,
        vad_model: str,
        punc_model: str,
        spk_model: str,
        speaker_enabled: bool,
        speaker_count: int,
        hotword: str,
        page_size: int,
        progress=gr.Progress()
    ) -> Tuple[str, gr.Column]:
        """开始语音识别并初始化分页编辑器"""
        
        if not audio_file:
            return "错误: 请先上传音频文件。", gr.Column()
        
        # 保存当前音频文件路径
        self.current_audio_file = audio_file
        self.current_audio_filename = os.path.splitext(os.path.basename(audio_file))[0]
        
        # 获取模型路径
        progress(0.1, desc="准备模型配置...")
        
        config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
        if os.path.exists(config_path):
            config.read(config_path, encoding='utf-8')
        
        # 获取ASR模型路径
        if model_type == "paraformer" and "asr_models_dir" in config:
            asr_model_path = config["asr_models_dir"].get(asr_model)
        elif model_type == "seaco" and "asr_seaco_models_dir" in config:
            asr_model_path = config["asr_seaco_models_dir"].get(asr_model)
        else:
            asr_model_path = self.asr_models_config.get(asr_model)
        
        if not asr_model_path:
            return "错误: 请选择一个有效的ASR模型。", gr.Column()
        
        # 获取其他模型路径
        vad_model_path = None if vad_model == "None (不使用)" else self.vad_models_config.get(vad_model)
        punc_model_path = None if punc_model == "None (不使用)" else self.punc_models_config.get(punc_model)
        spk_model_path = None if spk_model == "None (不使用)" else self.spk_models_config.get(spk_model)
        
        # 说话人识别相关参数
        speaker_enabled_for_recognition = speaker_enabled and spk_model_path
        num_speakers = speaker_count if speaker_enabled_for_recognition else 0
        
        status_info = []
        status_info.append(f"开始识别: {os.path.basename(audio_file)}")
        status_info.append(f"模型类型: {model_type}")
        status_info.append(f"设备: {device}")
        status_info.append(f"ASR模型: {asr_model}")
        
        try:
            progress(0.2, desc="加载识别模型...")
            
            # 构建模型缓存键
            model_key_parts = [
                device, asr_model_path, str(vad_model_path), str(punc_model_path),
                str(spk_model_path) if speaker_enabled_for_recognition else "no_spk",
                model_type, str(speaker_enabled_for_recognition)
            ]
            model_key = "_".join(filter(None, model_key_parts))
            
            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                status_info.append("从缓存加载模型")
            else:
                status_info.append("首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "model": asr_model_path,
                    "device": device,
                    "disable_update": True
                }
                
                # 根据模型类型设置参数
                if model_type == "paraformer":
                    model_kwargs["model_revision"] = "master"
                    model_kwargs["timestamp"] = True
                    if vad_model_path:
                        model_kwargs["vad_model"] = vad_model_path
                    if punc_model_path:
                        model_kwargs["punc_model"] = punc_model_path
                    if spk_model_path and speaker_enabled_for_recognition:
                        model_kwargs["spk_model"] = spk_model_path
                        
                elif model_type == "seaco":
                    model_kwargs["model_revision"] = "master"
                    model_kwargs["timestamp"] = True
                    if vad_model_path:
                        model_kwargs["vad_model"] = vad_model_path
                        model_kwargs["vad_kwargs"] = {"max_single_segment_time": 60000}
                    if punc_model_path:
                        model_kwargs["punc_model"] = punc_model_path
                    if spk_model_path and speaker_enabled_for_recognition:
                        model_kwargs["spk_model"] = spk_model_path
                
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                status_info.append("模型加载完成")
            
            progress(0.5, desc="开始语音识别...")
            
            # 准备识别参数
            generate_kwargs = {"input": audio_file}
            
            if model_type in ["paraformer", "seaco"]:
                generate_kwargs["cache"] = {}
                generate_kwargs["return_raw_text"] = True
                if spk_model_path and speaker_enabled_for_recognition and num_speakers > 0:
                    generate_kwargs['preset_spk_num'] = num_speakers
                if model_type == "seaco" and hotword.strip():
                    generate_kwargs['hotword'] = hotword.strip()
            
            start_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_time = time.time()
            processing_time = end_time - start_time
            
            progress(0.8, desc="处理识别结果...")
            
            # 处理识别结果
            output_lines, table_data = self._process_recognition_result(
                rec_result, speaker_enabled_for_recognition, speaker_enabled
            )
            
            # 创建缓存会话并保存识别结果
            if table_data:
                try:
                    self.cache_manager.create_session(self.current_audio_filename)
                    self.cache_manager.save_recognition_result(table_data, speaker_enabled)
                    status_info.append("识别结果已保存到缓存")
                except Exception as e:
                    status_info.append(f"保存到缓存失败: {e}")
            
            # 初始化分页编辑器
            segments = self.cache_manager.get_all_segments()
            self.paginated_editor.page_size = page_size
            self.paginated_editor.initialize_data(segments, audio_file)
            
            # 创建分页编辑器界面
            editor_column = self.create_paginated_editor_interface()
            
            status_info.append(f"识别完成，耗时: {processing_time:.2f} 秒")
            status_info.append(f"共识别出 {len(segments)} 个片段")
            final_status = "\n".join(status_info)
            
            progress(1.0, desc="识别完成")
            
            return final_status, editor_column
            
        except Exception as e:
            error_msg = f"识别过程中发生错误: {str(e)}"
            status_info.append(error_msg)
            return "\n".join(status_info), gr.Column()

    def _process_recognition_result(self, rec_result, speaker_enabled_for_recognition: bool, speaker_display_enabled: bool):
        """处理识别结果"""
        output_lines = []
        table_data = []
        
        if rec_result:
            subtitle_index = 1
            
            for result in rec_result:
                if 'sentence_info' in result:
                    for sentence in result['sentence_info']:
                        spk_value = sentence.get('spk')
                        if speaker_enabled_for_recognition and spk_value is not None and isinstance(spk_value, int):
                            speaker = f"spk{spk_value + 1}"
                            speaker_for_table = speaker if speaker_display_enabled else ""
                        else:
                            speaker_for_table = ""
                            
                        text = sentence.get('text', '').strip().rstrip(',.。，!！?？')
                        start_time = self.format_timestamp(sentence.get('start', 0), srt_format=True)
                        end_time = self.format_timestamp(sentence.get('end', 0), srt_format=True)
                        
                        # 为表格添加数据
                        if speaker_display_enabled:
                            table_data.append([
                                subtitle_index, start_time, end_time, speaker_for_table, text
                            ])
                        else:
                            table_data.append([
                                subtitle_index, start_time, end_time, text
                            ])
                        
                        subtitle_index += 1
        
        return output_lines, table_data

    def create_paginated_editor_interface(self):
        """创建分页编辑器界面"""
        with gr.Column() as editor_column:
            # 页面控制栏
            with gr.Row():
                prev_btn = gr.Button("◀ 前一页", size="sm")
                page_info = gr.HTML(f"<center>第 {self.paginated_editor.current_page} 页 / 共 {self.paginated_editor.total_pages} 页</center>")
                next_btn = gr.Button("后一页 ▶", size="sm")
            
            # 批量操作栏
            with gr.Row():
                save_all_btn = gr.Button("💾 保存全部", variant="primary", size="sm")
                export_audio_btn = gr.DownloadButton("🎵 导出音频", variant="secondary", size="sm")
                refresh_btn = gr.Button("🔄 刷新", size="sm")
            
            # 文本编辑卡片容器
            cards_container = gr.Column()
            
            # 初始化显示当前页面的卡片
            self.update_cards_display(cards_container)
        
        return editor_column

    def update_cards_display(self, container):
        """更新卡片显示"""
        page_data = self.paginated_editor.get_current_page_data()
        
        with container:
            for segment in page_data:
                self.create_segment_card(segment)

    def create_segment_card(self, segment):
        """创建单个片段编辑卡片"""
        segment_id = segment.get('id', 1)
        start_time = segment.get('start_time', '')
        end_time = segment.get('end_time', '')
        speaker = segment.get('speaker', '')
        text = segment.get('text', '')
        audio_file = segment.get('audio_file', None)
        
        with gr.Group():
            with gr.Row():
                # 左侧：文本编辑区域
                with gr.Column(scale=3):
                    gr.HTML(f"<h4>Text {segment_id}</h4>")
                    
                    text_input = gr.Textbox(
                        value=text,
                        label="识别文本",
                        lines=3,
                        max_lines=5,
                        interactive=True
                    )
                    
                    if speaker:
                        speaker_input = gr.Textbox(
                            value=speaker,
                            label="说话人",
                            lines=1,
                            interactive=True
                        )
                
                # 右侧：音频播放和控制
                with gr.Column(scale=2):
                    if audio_file and os.path.exists(audio_file):
                        audio_player = gr.Audio(
                            value=audio_file,
                            label="音频片段",
                            interactive=False,
                            show_download_button=True
                        )
                    else:
                        audio_player = gr.HTML(
                            "<div style='text-align: center; color: #666;'>音频处理中...</div>"
                        )
                    
                    # 时间信息
                    time_info = gr.HTML(
                        f"<small>{start_time} → {end_time}</small>"
                    )
                    
                    with gr.Row():
                        save_btn = gr.Button("💾", size="sm")
                        reset_btn = gr.Button("🔄", size="sm")

    def create_interface(self):
        """创建Gradio界面"""

        with gr.Blocks(title="Light语音识别WebUI - 分页版", theme=gr.themes.Soft()) as demo:

            # 标题
            gr.HTML("""
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #2c3e50; margin-bottom: 10px;">🎵 LightASR语音识别WebUI - 分页编辑版</h1>
                <p style="color: #7f8c8d; font-size: 16px;">
                    上传音频文件，选择运行设备和模型进行识别。支持分页编辑、音频片段播放和批量操作。
                </p>
            </div>
            """)

            with gr.Row():
                # 左侧控制面板
                with gr.Column(scale=3, min_width=400):
                    gr.HTML("<h3>🎛️ 控制面板</h3>")

                    # 音频文件上传
                    audio_file = gr.File(
                        label="📁 上传音频文件",
                        file_types=["audio"],
                        type="filepath"
                    )

                    # 基础配置
                    with gr.Group():
                        gr.HTML("<h4>⚙️ 基础配置</h4>")

                        model_type = gr.Dropdown(
                            choices=[("Paraformer-zh-spk", "paraformer"), ("Seaco Paraformer", "seaco")],
                            value="paraformer",
                            label="模型类型"
                        )

                        device = gr.Dropdown(
                            choices=[("CPU", "cpu"), ("CUDA", "cuda")],
                            value="cuda" if platform.system().lower() != 'darwin' else "cpu",
                            label="运行设备"
                        )

                    # 热词输入（仅Seaco显示）
                    with gr.Row(visible=False) as hotword_row:
                        hotword = gr.Textbox(
                            label="🔥 热词 (空格分隔)",
                            placeholder="请输入热词，使用空格分隔",
                            lines=1
                        )

                    # 说话人识别配置
                    with gr.Group():
                        gr.HTML("<h4>👥 说话人识别</h4>")

                        speaker_enabled = gr.Checkbox(
                            label="启用说话人识别",
                            value=True
                        )

                        speaker_count = gr.Number(
                            label="说话人数量 (0=自动识别)",
                            value=0,
                            minimum=0,
                            maximum=20,
                            step=1
                        )

                    # 分页配置
                    with gr.Group():
                        gr.HTML("<h4>📄 分页设置</h4>")

                        page_size = gr.Slider(
                            minimum=5,
                            maximum=50,
                            value=10,
                            step=5,
                            label="每页显示数量"
                        )

                    # 模型选择（可折叠，默认收起）
                    with gr.Accordion("🤖 模型配置", open=False):
                        # 获取默认模型值
                        paraformer_choices = self.get_model_choices("paraformer")
                        vad_choices = self.get_vad_model_choices()
                        punc_choices = self.get_punc_model_choices()
                        spk_choices = self.get_spk_model_choices()

                        asr_model = gr.Dropdown(
                            choices=paraformer_choices,
                            label="ASR模型",
                            value=paraformer_choices[0] if paraformer_choices else None
                        )

                        vad_model = gr.Dropdown(
                            choices=vad_choices,
                            label="VAD模型",
                            value=vad_choices[1] if len(vad_choices) > 1 else vad_choices[0] if vad_choices else None
                        )

                        punc_model = gr.Dropdown(
                            choices=punc_choices,
                            label="标点模型",
                            value=punc_choices[1] if len(punc_choices) > 1 else punc_choices[0] if punc_choices else None
                        )

                        spk_model = gr.Dropdown(
                            choices=spk_choices,
                            label="说话人模型",
                            value=spk_choices[1] if len(spk_choices) > 1 else spk_choices[0] if spk_choices else None
                        )

                    # 识别按钮
                    recognize_btn = gr.Button(
                        "🚀 开始识别",
                        variant="primary",
                        size="lg"
                    )

                    # 程序状态
                    gr.HTML("<h4>📊 程序状态</h4>")
                    status_output = gr.Textbox(
                        label="状态信息",
                        lines=8,
                        max_lines=15,
                        show_copy_button=True,
                        interactive=False
                    )

                # 右侧结果面板
                with gr.Column(scale=7, min_width=600):
                    gr.HTML("<h3>📋 识别结果编辑器</h3>")

                    # 编辑器容器
                    editor_container = gr.Column()

            # 事件绑定
            model_type.change(
                fn=self.update_model_choices,
                inputs=[model_type],
                outputs=[asr_model, hotword_row]
            )

            recognize_btn.click(
                fn=self.start_recognition,
                inputs=[
                    audio_file, model_type, device, asr_model, vad_model,
                    punc_model, spk_model, speaker_enabled, speaker_count, hotword, page_size
                ],
                outputs=[status_output, editor_container]
            )

        return demo

def main():
    """主函数"""
    # 创建WebUI实例
    webui = AsrWebUIPaginated()

    # 创建界面
    demo = webui.create_interface()

    # 启动界面
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,  # 使用不同端口避免冲突
        share=False,
        debug=False,
        favicon_path=None,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
