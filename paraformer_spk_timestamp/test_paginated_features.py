#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页编辑器功能测试脚本
"""

import gradio as gr
import json
import tempfile
from typing import List, Dict, Tuple

class PaginatedEditorTest:
    def __init__(self):
        self.current_page = 1
        self.page_size = 10
        self.total_pages = 1
        self.segments_data = []
        self.modified_segments = set()
        
    def generate_test_data(self, num_segments: int = 25) -> str:
        """生成测试数据"""
        demo_texts = [
            "欢迎使用语音识别系统，这是一个功能强大的工具。",
            "我们今天的会议主要讨论项目进展情况。",
            "请大家准时参加，会议将在上午九点开始。",
            "关于技术方案，我们需要进一步优化算法。",
            "用户体验是我们最关心的问题之一。",
            "希望大家能够积极参与讨论，提出宝贵意见。",
            "下一步我们将开始系统测试阶段。",
            "测试完成后，我们会进行产品发布。",
            "感谢大家的辛勤工作和支持。",
            "如果有任何问题，请随时联系我。",
            "我们的目标是打造最好的语音识别产品。",
            "技术创新是我们发展的核心动力。",
            "客户满意度是衡量成功的重要指标。",
            "团队合作精神让我们走得更远。",
            "持续学习和改进是我们的理念。",
            "质量第一，用户至上是我们的原则。",
            "创新思维帮助我们解决复杂问题。",
            "数据安全和隐私保护非常重要。",
            "我们致力于提供最佳的服务体验。",
            "未来发展充满机遇和挑战。",
            "让我们一起努力，创造更美好的明天。",
            "技术进步推动社会发展。",
            "人工智能改变我们的生活方式。",
            "语音识别技术应用前景广阔。",
            "我们要保持开放和包容的心态。"
        ]
        
        self.segments_data = []
        for i in range(1, num_segments + 1):
            text_idx = (i - 1) % len(demo_texts)
            text = demo_texts[text_idx]
            
            # 生成时间戳
            start_seconds = (i - 1) * 3
            end_seconds = i * 3
            start_time = f"00:{start_seconds//60:02d}:{start_seconds%60:02d},000"
            end_time = f"00:{end_seconds//60:02d}:{end_seconds%60:02d},000"
            
            segment = {
                "id": i,
                "start_time": start_time,
                "end_time": end_time,
                "speaker": f"spk{(i-1)%4+1}",  # 4个说话人
                "text": text,
                "original_text": text
            }
            self.segments_data.append(segment)
        
        self.total_pages = max(1, (len(self.segments_data) + self.page_size - 1) // self.page_size)
        self.current_page = 1
        self.modified_segments.clear()
        
        return f"生成了 {len(self.segments_data)} 个测试片段，共 {self.total_pages} 页"
    
    def get_current_page_data(self) -> List[Dict]:
        """获取当前页面数据"""
        start_idx = (self.current_page - 1) * self.page_size
        end_idx = min(start_idx + self.page_size, len(self.segments_data))
        return self.segments_data[start_idx:end_idx]
    
    def navigate_page(self, direction: str) -> Tuple[str, str, str]:
        """页面导航"""
        if direction == "prev" and self.current_page > 1:
            self.current_page -= 1
        elif direction == "next" and self.current_page < self.total_pages:
            self.current_page += 1
        
        return self.update_display()
    
    def change_page_size(self, new_size: int) -> Tuple[str, str, str]:
        """修改页面大小"""
        self.page_size = int(new_size)
        self.total_pages = max(1, (len(self.segments_data) + self.page_size - 1) // self.page_size)
        
        # 调整当前页面
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages
        
        return self.update_display()
    
    def update_display(self) -> Tuple[str, str, str]:
        """更新显示"""
        page_data = self.get_current_page_data()
        cards_html = self.generate_cards_html(page_data)
        
        # 页面信息
        page_info = f"第 {self.current_page} 页 / 共 {self.total_pages} 页 (共 {len(self.segments_data)} 个片段)"
        
        # 统计信息
        modified_count = len(self.modified_segments)
        stats_info = f"已修改: {modified_count} 个片段 | 当前页显示: {len(page_data)} 个片段"
        
        return page_info, stats_info, cards_html
    
    def generate_cards_html(self, page_data: List[Dict]) -> str:
        """生成卡片HTML"""
        if not page_data:
            return "<div style='text-align: center; padding: 40px; color: #666;'>暂无数据</div>"
        
        html_parts = []
        
        for segment in page_data:
            segment_id = segment.get("id", 1)
            start_time = segment.get("start_time", "")
            end_time = segment.get("end_time", "")
            speaker = segment.get("speaker", "")
            text = segment.get("text", "")
            
            # 状态指示器
            is_modified = segment_id in self.modified_segments
            status_indicator = "🔴" if is_modified else "🟢"
            status_text = "已修改" if is_modified else "原始"
            
            card_html = f"""
            <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; margin-bottom: 16px; 
                        background: #ffffff; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div style="display: flex; gap: 16px;">
                    <!-- 左侧：文本编辑区域 -->
                    <div style="flex: 1;">
                        <h4 style="margin: 0 0 12px 0; color: #333; display: flex; align-items: center; gap: 8px;">
                            📝 Text {segment_id} 
                            <span style="font-size: 12px; color: #666;">{status_indicator} {status_text}</span>
                        </h4>
                        
                        <div style="margin-bottom: 12px;">
                            <label style="display: block; margin-bottom: 4px; font-weight: bold; color: #555;">识别文本:</label>
                            <textarea id="text_{segment_id}" style="width: 100%; min-height: 80px; padding: 12px; 
                                     border: 1px solid #d0d0d0; border-radius: 6px; font-size: 14px; line-height: 1.5; 
                                     resize: vertical; font-family: inherit;" 
                                     onchange="markAsModified({segment_id})">{text}</textarea>
                        </div>
                        
                        <div style="margin-bottom: 12px;">
                            <label style="display: block; margin-bottom: 4px; font-weight: bold; color: #555;">说话人:</label>
                            <input type="text" id="speaker_{segment_id}" value="{speaker}" 
                                   style="width: 100%; padding: 8px 12px; border: 1px solid #d0d0d0; 
                                          border-radius: 6px; font-size: 14px; font-family: inherit;"
                                   onchange="markAsModified({segment_id})">
                        </div>
                    </div>
                    
                    <!-- 右侧：音频播放和控制 -->
                    <div style="width: 300px; display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 100%; height: 60px; background-color: #f5f5f5; border: 1px dashed #ccc; 
                                    border-radius: 6px; display: flex; justify-content: center; align-items: center; 
                                    color: #666; font-size: 14px; margin-bottom: 12px;">
                            🎵 音频片段 (演示)
                        </div>
                        
                        <div style="font-size: 12px; color: #666; margin-bottom: 12px; text-align: center;">
                            {start_time} → {end_time}
                        </div>
                        
                        <div style="display: flex; gap: 8px;">
                            <button onclick="saveSegment({segment_id})" 
                                    style="background-color: #4CAF50; color: white; border: none; padding: 8px 16px; 
                                           border-radius: 4px; cursor: pointer; font-size: 12px; transition: background-color 0.3s ease;">
                                💾 保存
                            </button>
                            <button onclick="resetSegment({segment_id})" 
                                    style="background-color: #f44336; color: white; border: none; padding: 8px 16px; 
                                           border-radius: 4px; cursor: pointer; font-size: 12px; transition: background-color 0.3s ease;">
                                🔄 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            """
            html_parts.append(card_html)
        
        # 添加JavaScript
        js_code = f"""
        <script>
        let modifiedSegments = new Set({list(self.modified_segments)});
        
        function markAsModified(segmentId) {{
            modifiedSegments.add(segmentId);
            console.log('Segment', segmentId, 'marked as modified');
        }}
        
        function saveSegment(segmentId) {{
            const textElement = document.getElementById('text_' + segmentId);
            const speakerElement = document.getElementById('speaker_' + segmentId);
            
            if (textElement && speakerElement) {{
                const newText = textElement.value;
                const newSpeaker = speakerElement.value;
                
                // 模拟保存操作
                alert('保存片段 ' + segmentId + ':\\n文本: ' + newText + '\\n说话人: ' + newSpeaker);
                
                // 标记为已修改
                markAsModified(segmentId);
            }}
        }}
        
        function resetSegment(segmentId) {{
            if (confirm('确定要重置片段 ' + segmentId + ' 到原始状态吗？')) {{
                // 这里需要从原始数据恢复
                alert('片段 ' + segmentId + ' 已重置到原始状态');
                modifiedSegments.delete(segmentId);
            }}
        }}
        </script>
        """
        
        return "\n".join(html_parts) + js_code
    
    def export_results(self) -> str:
        """导出结果"""
        # 生成SRT格式内容
        lines = []
        for segment in self.segments_data:
            lines.append(str(segment["id"]))
            lines.append(f"{segment['start_time']} --> {segment['end_time']}")
            lines.append(f"[{segment['speaker']}] {segment['text']}")
            lines.append("")
        
        content = "\n".join(lines)
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8')
        temp_file.write(content)
        temp_file.close()
        
        return temp_file.name
    
    def create_interface(self):
        """创建Gradio界面"""
        
        with gr.Blocks(title="分页编辑器功能测试", theme=gr.themes.Soft()) as demo:
            
            # 标题
            gr.HTML("""
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #2c3e50; margin-bottom: 10px;">🧪 分页编辑器功能测试</h1>
                <p style="color: #7f8c8d; font-size: 16px;">
                    测试分页编辑器的各项功能：分页导航、动态调整、状态跟踪等
                </p>
            </div>
            """)
            
            # 控制面板
            with gr.Row():
                with gr.Column(scale=1):
                    gr.HTML("<h3>🎛️ 控制面板</h3>")
                    
                    # 数据生成
                    with gr.Group():
                        gr.HTML("<h4>📊 数据生成</h4>")
                        num_segments = gr.Number(
                            label="生成片段数量",
                            value=25,
                            minimum=5,
                            maximum=100,
                            step=5
                        )
                        generate_btn = gr.Button("🎲 生成测试数据", variant="primary")
                    
                    # 分页控制
                    with gr.Group():
                        gr.HTML("<h4>📄 分页控制</h4>")
                        
                        with gr.Row():
                            prev_btn = gr.Button("◀ 前一页", size="sm")
                            next_btn = gr.Button("后一页 ▶", size="sm")
                        
                        page_size_slider = gr.Slider(
                            minimum=5,
                            maximum=25,
                            value=10,
                            step=5,
                            label="每页显示数量"
                        )
                    
                    # 批量操作
                    with gr.Group():
                        gr.HTML("<h4>🔧 批量操作</h4>")
                        export_btn = gr.DownloadButton("📄 导出SRT", variant="secondary")
                    
                    # 状态信息
                    gr.HTML("<h4>📊 状态信息</h4>")
                    page_info = gr.Textbox(
                        label="页面信息",
                        interactive=False
                    )
                    stats_info = gr.Textbox(
                        label="统计信息",
                        interactive=False
                    )
                
                # 编辑器区域
                with gr.Column(scale=2):
                    gr.HTML("<h3>📝 编辑器区域</h3>")
                    
                    editor_container = gr.HTML(
                        value="<div style='text-align: center; padding: 40px; color: #666;'>请先生成测试数据</div>"
                    )
            
            # 事件绑定
            generate_btn.click(
                fn=lambda x: self.generate_test_data(int(x)),
                inputs=[num_segments],
                outputs=[stats_info]
            ).then(
                fn=self.update_display,
                outputs=[page_info, stats_info, editor_container]
            )
            
            prev_btn.click(
                fn=lambda: self.navigate_page("prev"),
                outputs=[page_info, stats_info, editor_container]
            )
            
            next_btn.click(
                fn=lambda: self.navigate_page("next"),
                outputs=[page_info, stats_info, editor_container]
            )
            
            page_size_slider.change(
                fn=self.change_page_size,
                inputs=[page_size_slider],
                outputs=[page_info, stats_info, editor_container]
            )
            
            export_btn.click(
                fn=self.export_results,
                outputs=[export_btn]
            )
        
        return demo

def main():
    """主函数"""
    test_app = PaginatedEditorTest()
    demo = test_app.create_interface()
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7864,
        share=False,
        debug=True,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
