/* 分页编辑器样式 */

/* 卡片样式 */
.segment-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.segment-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 文本编辑区域 */
.text-editor {
    flex: 1;
    margin-right: 16px;
}

.text-input {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 1px solid #d0d0d0;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
}

.text-input:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.speaker-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d0d0d0;
    border-radius: 6px;
    font-size: 14px;
    margin-top: 8px;
}

/* 音频播放区域 */
.audio-section {
    width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.audio-player {
    width: 100%;
    margin-bottom: 12px;
}

.time-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
    text-align: center;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-save {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.btn-save:hover {
    background-color: #45a049;
}

.btn-reset {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.btn-reset:hover {
    background-color: #da190b;
}

/* 分页控制 */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin: 20px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.page-info {
    font-weight: bold;
    color: #333;
    min-width: 150px;
    text-align: center;
}

.btn-page {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn-page:hover:not(:disabled) {
    background-color: #1976D2;
}

.btn-page:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 批量操作栏 */
.batch-operations {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding: 12px 16px;
    background: #e3f2fd;
    border-radius: 8px;
    border-left: 4px solid #2196F3;
}

.batch-buttons {
    display: flex;
    gap: 12px;
}

.btn-primary {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #1976D2;
}

.btn-secondary {
    background-color: #757575;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: #616161;
}

/* 页面大小滑块 */
.page-size-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-size-control label {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
}

/* 滚动容器 */
.scrollable-content {
    max-height: 80vh;
    overflow-y: auto;
    padding-right: 8px;
}

.scrollable-content::-webkit-scrollbar {
    width: 8px;
}

.scrollable-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .segment-card {
        flex-direction: column;
    }
    
    .audio-section {
        width: 100%;
        margin-top: 16px;
    }
    
    .text-editor {
        margin-right: 0;
        margin-bottom: 16px;
    }
    
    .pagination-controls {
        flex-direction: column;
        gap: 12px;
    }
    
    .batch-operations {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .batch-buttons {
        justify-content: center;
    }
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #666;
}

.loading::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 4px solid #f44336;
    margin: 16px 0;
}

/* 成功状态 */
.success-message {
    background-color: #e8f5e8;
    color: #2e7d32;
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 4px solid #4caf50;
    margin: 16px 0;
}

/* 音频不可用状态 */
.audio-unavailable {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    background-color: #f5f5f5;
    border: 1px dashed #ccc;
    border-radius: 6px;
    color: #666;
    font-size: 14px;
}

/* 段落标题 */
.segment-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.segment-title::before {
    content: "📝";
    font-size: 14px;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}
