#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版分页编辑器WebUI - 用于测试基本功能
"""

import gradio as gr
import os
import sys
import json
from typing import List, Dict

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
from web_cache_manager import WebCacheManager

class SimplePagedEditor:
    def __init__(self):
        self.cache_manager = WebCacheManager()
        self.current_page = 1
        self.page_size = 10
        self.total_pages = 1
        self.segments_data = []
        
    def generate_demo_data(self):
        """生成演示数据"""
        demo_texts = [
            "欢迎使用语音识别系统，这是一个功能强大的工具。",
            "我们今天的会议主要讨论项目进展情况。",
            "请大家准时参加，会议将在上午九点开始。",
            "关于技术方案，我们需要进一步优化算法。",
            "用户体验是我们最关心的问题之一。",
            "希望大家能够积极参与讨论，提出宝贵意见。",
            "下一步我们将开始系统测试阶段。",
            "测试完成后，我们会进行产品发布。",
            "感谢大家的辛勤工作和支持。",
            "如果有任何问题，请随时联系我。",
            "我们的目标是打造最好的语音识别产品。",
            "技术创新是我们发展的核心动力。",
            "客户满意度是衡量成功的重要指标。",
            "团队合作精神让我们走得更远。",
            "持续学习和改进是我们的理念。",
            "质量第一，用户至上是我们的原则。",
            "创新思维帮助我们解决复杂问题。",
            "数据安全和隐私保护非常重要。",
            "我们致力于提供最佳的服务体验。",
            "未来发展充满机遇和挑战。",
            "让我们一起努力，创造更美好的明天。",
            "技术进步推动社会发展。",
            "人工智能改变我们的生活方式。",
            "语音识别技术应用前景广阔。",
            "我们要保持开放和包容的心态。"
        ]
        
        self.segments_data = []
        for i, text in enumerate(demo_texts, 1):
            # 生成时间戳
            start_minutes = (i - 1) * 2
            end_minutes = i * 2
            start_time = f"00:{start_minutes:02d}:{(i-1)*5%60:02d},000"
            end_time = f"00:{end_minutes:02d}:{i*5%60:02d},000"
            
            segment = {
                "id": i,
                "start_time": start_time,
                "end_time": end_time,
                "speaker": f"spk{(i-1)%3+1}",
                "text": text
            }
            self.segments_data.append(segment)
        
        self.total_pages = max(1, (len(self.segments_data) + self.page_size - 1) // self.page_size)
        return f"生成了 {len(self.segments_data)} 个演示片段"
    
    def get_current_page_data(self) -> List[Dict]:
        """获取当前页面数据"""
        start_idx = (self.current_page - 1) * self.page_size
        end_idx = min(start_idx + self.page_size, len(self.segments_data))
        return self.segments_data[start_idx:end_idx]
    
    def generate_cards_html(self, page_data: List[Dict]) -> str:
        """生成卡片HTML"""
        if not page_data:
            return "<div style='text-align: center; padding: 40px; color: #666;'>暂无数据</div>"
        
        html_parts = []
        
        for segment in page_data:
            segment_id = segment.get("id", 1)
            start_time = segment.get("start_time", "")
            end_time = segment.get("end_time", "")
            speaker = segment.get("speaker", "")
            text = segment.get("text", "")
            
            card_html = f"""
            <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; margin-bottom: 16px; 
                        background: #ffffff; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div style="display: flex; gap: 16px;">
                    <!-- 左侧：文本编辑区域 -->
                    <div style="flex: 1;">
                        <h4 style="margin: 0 0 12px 0; color: #333; display: flex; align-items: center; gap: 8px;">
                            📝 Text {segment_id} 
                            <span style="font-size: 12px; color: #666;">🟢 原始</span>
                        </h4>
                        
                        <div style="margin-bottom: 12px;">
                            <label style="display: block; margin-bottom: 4px; font-weight: bold; color: #555;">识别文本:</label>
                            <textarea id="text_{segment_id}" style="width: 100%; min-height: 80px; padding: 12px; 
                                     border: 1px solid #d0d0d0; border-radius: 6px; font-size: 14px; line-height: 1.5; 
                                     resize: vertical; font-family: inherit;">{text}</textarea>
                        </div>
                        
                        <div style="margin-bottom: 12px;">
                            <label style="display: block; margin-bottom: 4px; font-weight: bold; color: #555;">说话人:</label>
                            <input type="text" id="speaker_{segment_id}" value="{speaker}" 
                                   style="width: 100%; padding: 8px 12px; border: 1px solid #d0d0d0; 
                                          border-radius: 6px; font-size: 14px; font-family: inherit;">
                        </div>
                    </div>
                    
                    <!-- 右侧：音频播放和控制 -->
                    <div style="width: 300px; display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 100%; height: 60px; background-color: #f5f5f5; border: 1px dashed #ccc; 
                                    border-radius: 6px; display: flex; justify-content: center; align-items: center; 
                                    color: #666; font-size: 14px; margin-bottom: 12px;">
                            🎵 音频片段 (演示)
                        </div>
                        
                        <div style="font-size: 12px; color: #666; margin-bottom: 12px; text-align: center;">
                            {start_time} → {end_time}
                        </div>
                        
                        <div style="display: flex; gap: 8px;">
                            <button onclick="saveSegment({segment_id})" 
                                    style="background-color: #4CAF50; color: white; border: none; padding: 8px 16px; 
                                           border-radius: 4px; cursor: pointer; font-size: 12px; transition: background-color 0.3s ease;">
                                💾 保存
                            </button>
                            <button onclick="resetSegment({segment_id})" 
                                    style="background-color: #f44336; color: white; border: none; padding: 8px 16px; 
                                           border-radius: 4px; cursor: pointer; font-size: 12px; transition: background-color 0.3s ease;">
                                🔄 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            """
            html_parts.append(card_html)
        
        return "\n".join(html_parts)
    
    def navigate_page(self, direction: str):
        """页面导航"""
        if direction == "prev" and self.current_page > 1:
            self.current_page -= 1
        elif direction == "next" and self.current_page < self.total_pages:
            self.current_page += 1
        
        return self.update_display()
    
    def change_page_size(self, new_size: int):
        """修改页面大小"""
        self.page_size = int(new_size)
        self.total_pages = max(1, (len(self.segments_data) + self.page_size - 1) // self.page_size)
        
        # 调整当前页面
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages
        
        return self.update_display()
    
    def update_display(self):
        """更新显示"""
        page_data = self.get_current_page_data()
        cards_html = self.generate_cards_html(page_data)
        
        # 生成完整的界面HTML
        interface_html = f"""
        <div style="padding: 20px;">
            <!-- 页面控制栏 -->
            <div style="display: flex; justify-content: center; align-items: center; gap: 16px; margin: 20px 0; 
                        padding: 16px; background: #f8f9fa; border-radius: 8px;">
                <button onclick="navigatePage('prev')" 
                        style="background-color: {'#2196F3' if self.current_page > 1 else '#ccc'}; color: white; border: none; 
                               padding: 10px 20px; border-radius: 6px; cursor: {'pointer' if self.current_page > 1 else 'not-allowed'}; 
                               font-size: 14px;" {'disabled' if self.current_page <= 1 else ''}>
                    ◀ 前一页
                </button>
                <div style="font-weight: bold; color: #333; min-width: 150px; text-align: center;">
                    第 {self.current_page} 页 / 共 {self.total_pages} 页
                </div>
                <button onclick="navigatePage('next')" 
                        style="background-color: {'#2196F3' if self.current_page < self.total_pages else '#ccc'}; color: white; border: none; 
                               padding: 10px 20px; border-radius: 6px; cursor: {'pointer' if self.current_page < self.total_pages else 'not-allowed'}; 
                               font-size: 14px;" {'disabled' if self.current_page >= self.total_pages else ''}>
                    后一页 ▶
                </button>
            </div>
            
            <!-- 批量操作栏 -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin: 16px 0; 
                        padding: 12px 16px; background: #e3f2fd; border-radius: 8px; border-left: 4px solid #2196F3;">
                <div style="display: flex; gap: 12px;">
                    <button onclick="saveAllChanges()" style="background-color: #2196F3; color: white; border: none; 
                            padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;">
                        💾 保存全部
                    </button>
                    <button onclick="exportResults()" style="background-color: #757575; color: white; border: none; 
                            padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;">
                        📄 导出结果
                    </button>
                </div>
                <div style="display: flex; align-items: center; gap: 12px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">每页显示:</label>
                    <select onchange="changePageSize(this.value)" style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="5" {'selected' if self.page_size == 5 else ''}>5</option>
                        <option value="10" {'selected' if self.page_size == 10 else ''}>10</option>
                        <option value="15" {'selected' if self.page_size == 15 else ''}>15</option>
                        <option value="20" {'selected' if self.page_size == 20 else ''}>20</option>
                    </select>
                </div>
            </div>
            
            <!-- 文本编辑卡片 -->
            <div style="max-height: 70vh; overflow-y: auto; padding-right: 8px;">
                {cards_html}
            </div>
        </div>
        
        <script>
        function navigatePage(direction) {{
            // 这里需要通过Gradio的接口来调用Python函数
            console.log('Navigate:', direction);
        }}
        
        function saveAllChanges() {{
            alert('保存全部修改功能');
        }}
        
        function exportResults() {{
            alert('导出结果功能');
        }}
        
        function changePageSize(size) {{
            console.log('Change page size:', size);
        }}
        
        function saveSegment(segmentId) {{
            const textElement = document.getElementById('text_' + segmentId);
            const speakerElement = document.getElementById('speaker_' + segmentId);
            
            if (textElement && speakerElement) {{
                const newText = textElement.value;
                const newSpeaker = speakerElement.value;
                alert('保存片段 ' + segmentId + ':\\n文本: ' + newText + '\\n说话人: ' + newSpeaker);
            }}
        }}
        
        function resetSegment(segmentId) {{
            if (confirm('确定要重置片段 ' + segmentId + ' 到原始状态吗？')) {{
                alert('片段 ' + segmentId + ' 已重置');
            }}
        }}
        </script>
        """
        
        return interface_html
    
    def create_interface(self):
        """创建Gradio界面"""
        
        with gr.Blocks(title="简化版分页编辑器", theme=gr.themes.Soft()) as demo:
            
            # 标题
            gr.HTML("""
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #2c3e50; margin-bottom: 10px;">📝 简化版分页编辑器</h1>
                <p style="color: #7f8c8d; font-size: 16px;">
                    测试分页编辑功能的基本实现
                </p>
            </div>
            """)
            
            # 控制按钮
            with gr.Row():
                generate_btn = gr.Button("🎲 生成演示数据", variant="primary")
                prev_btn = gr.Button("◀ 前一页", size="sm")
                next_btn = gr.Button("后一页 ▶", size="sm")
                page_size_dropdown = gr.Dropdown(
                    choices=[5, 10, 15, 20],
                    value=10,
                    label="每页显示数量"
                )
            
            # 状态信息
            status_output = gr.Textbox(
                label="状态信息",
                lines=2,
                interactive=False
            )
            
            # 编辑器容器
            editor_container = gr.HTML()
            
            # 事件绑定
            generate_btn.click(
                fn=self.generate_demo_data,
                outputs=[status_output]
            ).then(
                fn=self.update_display,
                outputs=[editor_container]
            )
            
            prev_btn.click(
                fn=lambda: self.navigate_page("prev"),
                outputs=[editor_container]
            )
            
            next_btn.click(
                fn=lambda: self.navigate_page("next"),
                outputs=[editor_container]
            )
            
            page_size_dropdown.change(
                fn=self.change_page_size,
                inputs=[page_size_dropdown],
                outputs=[editor_container]
            )
        
        return demo

def main():
    """主函数"""
    editor = SimplePagedEditor()
    demo = editor.create_interface()
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        debug=True,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
